<!-- pages/user/userProfile/userProfile.wxml -->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <view class="user-header">
      <view class="user-avatar">
        <van-image round width="120rpx" height="120rpx" src="{{ userInfo.avatar }}" />
      </view>
      <view class="user-info">
        <view class="user-name">{{ userInfo.nickname }}</view>
        <view class="user-signature">{{ userInfo.slogan || '这个人很懒，什么都没留下' }}</view>
      </view>
    </view>

    <view class="user-bottom">
      <view class="user-stats">
        <view class="stat-item" bindtap="viewFollowList">
          <view class="stat-value">{{ followCount }}</view>
          <view class="stat-label">关注</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item" bindtap="viewFansList">
          <view class="stat-value">{{ fansCount }}</view>
          <view class="stat-label">粉丝</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-value">{{ productCount }}</view>
          <view class="stat-label">在售</view>
        </view>
      </view>

      <view class="action-buttons" wx:if="{{ userId !== (app.globalData.userInfo && app.globalData.userInfo.id) }}">
        <van-button
          type="{{ isFollowed ? 'primary' : 'info' }}"
          plain="{{ isFollowed }}"
          round
          size="small"
          bindtap="toggleFollow">
          {{ isFollowed ? '已关注' : '关注' }}
        </van-button>
        <button
          class="share-button"
          open-type="share">
          <van-icon name="wechat" size="16px" />
          <text>分享</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 内容区域 - Tab切换 -->
  <view class="content-section">
    <!-- Tab 切换栏 -->
    <view class="tab-bar">
      <view class="tab-item {{ activeTab === 'product' ? 'active' : '' }}" bindtap="switchTab" data-tab="product">
        <text>Ta的闲置</text>
      </view>
      <view class="tab-item {{ activeTab === 'activity' ? 'active' : '' }}" bindtap="switchTab" data-tab="activity">
        <text>Ta的活动</text>
      </view>
    </view>

    <!-- 商品列表区域 -->
    <view class="tab-content" wx:if="{{ activeTab === 'product' }}">
      <view class="product-list" wx:if="{{ productList.length > 0 }}">
        <view class="product-item" wx:for="{{ productList }}" wx:key="id" bindtap="viewProduct" data-id="{{ item.id }}">
          <van-image width="100%" height="200rpx" fit="cover" src="{{ item.firstImage }}" />
          <view class="product-info">
            <view class="product-name">{{ item.description }}</view>
            <view class="product-price">
              <text wx:if="{{item.price == 0}}">免费送</text>
              <text wx:else>¥{{ item.price }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="empty-state" wx:else>
        <van-empty description="暂无在售商品" />
      </view>
    </view>

    <!-- 活动列表区域 -->
    <view class="tab-content" wx:if="{{ activeTab === 'activity' }}">
      <view class="activity-list" wx:if="{{ activityList.length > 0 }}">
        <view class="activity-item" wx:for="{{ activityList }}" wx:key="id" bindtap="viewActivity" data-id="{{ item.id }}">
          <view class="activity-image-container">
            <van-image class="activity-image" width="100%" height="240rpx" fit="cover" src="{{ item.imageUrl || '/static/img/activity.png' }}" />
            <view class="activity-status-overlay">
              <van-tag custom-class="status-tag" type="{{ item.status === '1' ? 'primary' : 'warning' }}">{{ item.statusName }}</van-tag>
            </view>
          </view>
          <view class="activity-info">
            <view class="activity-title">{{ item.title }}</view>
            <view class="activity-details">
              <view class="activity-time">
                <van-icon name="clock-o" size="14px" color="#666" />
                <text>{{ item.formattedStartTime }}</text>
              </view>
              <view class="activity-location" wx:if="{{ item.location }}">
                <van-icon name="location-o" size="14px" color="#666" />
                <text>{{ item.locationName || '查看地点' }}</text>
              </view>
              <view class="activity-participants" wx:if="{{ item.participantCount !== undefined }}">
                <van-icon name="friends-o" size="14px" color="#666" />
                <text>{{ item.participantCount || 0 }}人参与</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="empty-state" wx:else>
        <van-empty description="暂无发布活动" />
      </view>
    </view>
  </view>
</view>
