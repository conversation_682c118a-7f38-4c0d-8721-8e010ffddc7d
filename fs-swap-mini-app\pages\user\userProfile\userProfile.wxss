/* pages/user/userProfile/userProfile.wxss */
.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 40rpx;
  padding-top: 200rpx;
}

.user-info-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  padding: 24rpx 30rpx 16rpx;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 24rpx;
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-signature {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.user-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12rpx;
  border-top: 1px solid #f2f2f2;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.share-button {
  background-color: #07c160;
  border: 1px solid #07c160;
  border-radius: 999px;
  color: #fff;
  font-size: 24rpx;
  height: 60rpx;
  line-height: 58rpx;
  padding: 0 24rpx;
  min-width: 100rpx;
  text-align: center;
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  gap: 8rpx;
}

.share-button::after {
  border: none;
}

.user-stats {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.stat-divider {
  width: 1px;
  height: 50rpx;
  background-color: #f2f2f2;
}

/* 固定Tab栏样式 */
.tab-bar-fixed {
  position: fixed;
  top: 240rpx;
  left: 0;
  right: 0;
  z-index: 99;
  background-color: #fff;
  display: flex;
  border-bottom: 1px solid #f2f2f2;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content-section {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  margin: 0 20rpx;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* Tab 切换栏样式 */
.tab-bar {
  display: flex;
  border-bottom: 1px solid #f2f2f2;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1989fa;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #1989fa;
  border-radius: 2rpx;
}

/* Tab 内容区域 */
.tab-content {
  padding: 30rpx;
}

.product-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.product-item {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.product-info {
  padding: 16rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff4757;
}

.empty-state {
  padding: 60rpx 0;
}

/* 活动列表样式 */
.activity-section {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin: 20rpx 20rpx 0;
}

.activity-list {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 24rpx;
}

.activity-item {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.activity-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.activity-image-container {
  position: relative;
  width: 100%;
}

.activity-image {
  width: 100%;
  border-radius: 16rpx 16rpx 0 0;
}

.activity-status-overlay {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  z-index: 1;
}

.status-tag {
  border-radius: 8rpx !important;
  padding: 4rpx 12rpx !important;
  font-size: 22rpx !important;
  font-weight: 500 !important;
}

.activity-info {
  padding: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.activity-time, .activity-location, .activity-participants {
  font-size: 24rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.activity-time text, .activity-location text, .activity-participants text {
  margin-left: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.activity-location text {
  max-width: 400rpx;
}
